/* You can add global styles to this file, and also import other style files */
 
body {
  overflow-x: hidden;
}
/* TABS CONTAINER */
.nav-tabs {
  background-color: white;
  border-bottom: none;
}

/* DEFAULT LINK STYLE */
.nav-tabs .nav-link {
  color: rgb(79, 79, 255);
  font-weight: 500;
  border-radius: 0;
  transition: all 0.2s ease;
}

/* HOVER EFFECT */
.nav-tabs .nav-link:hover {
  background-color: rgb(0, 0, 70);
  color: white;
}

/* ACTIVE (SELECTED) TAB */
.nav-tabs .nav-link.active {
  background-color: rgb(0, 0, 70);
  color: white;
}

/* DROPDOWN ON HOVER */
.nav-item.dropdown:hover .dropdown-menu {
  display: block;
}

/* DROPDOWN STYLING */
.dropdown-menu {
  width: 100%;
  top: 100%;
  left: 0;
  padding: .5em;
  border-radius: 5px;
  background-color: #ffffff;
  border: 1px solid rgb(229, 229, 255);
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* DROPDOWN ITEMS */
.dropdown-item {
  text-align: center;
  border: 1px solid rgb(218, 218, 218);
  transition: all 0.2s ease;
}

.dropdown-item:hover,
.dropdown-item.active {
  background-color: rgb(0, 0, 70);
  color: white;
}

#print-header,
.hideOnPage{
  display: none !important;
}





@media print {
  body {
    background: white !important;
  }

  .sidebar,
  .subnav{
    display: none;
  }

  .modal,
  .modal-backdrop {
    display: none !important;
  }

  .flex-grow-1 {
    margin-left: 0 !important;
  }

  #print-header {
    display: block !important;
  }

  .hideOnPage{
    display: flex !important;
  }

  button,
  #hideOnPrint {
    display: none !important;
  }
}
