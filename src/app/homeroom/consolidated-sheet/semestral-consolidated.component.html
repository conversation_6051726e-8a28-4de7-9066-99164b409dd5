<div 
  style="max-width: 1200px; height: 500px; overflow: auto; margin: auto; width: 100%;"
>
  <table class="table table-hover table-sm text-center" *ngIf="sheet" style="border-collapse: collapse;">
    <thead>
      <!-- First row: Subject names -->
      <tr>
        <th  
            style="position: sticky; left: 0; top: 0; background: #fff; z-index: 3;">
        </th>
        <th *ngFor="let subj of sheet.subjects" colspan="3" 
            style="position: sticky; top: 0; background: #fff; z-index: 2;">
          {{ subj.subject.name }}
        </th>
      </tr>

      <!-- Second row: 1ST / 2ND / SFG -->
      <tr style="position: sticky; left: 0; top: 150px; background: #fff; z-index: 3;">
        <th
            >
          Students
        </th>
        <ng-container *ngFor="let subj of sheet.subjects">
          <th style=" background: #fff; z-index: 2;">1ST</th>
          <th style=" background: #fff; z-index: 2;">2ND</th>
          <th style=" background: #fff; z-index: 2;">SFG</th>
        </ng-container>
      </tr>
    </thead>

    <tbody>
      <tr *ngFor="let student of sheet.students">
        <td style="position: sticky; left: 0; background: #fff; z-index: 1;">
          {{ student.name }}
        </td>
        <ng-container *ngFor="let subj of sheet.subjects">
          <td>{{ student.grades[subj.subject.id]?.first ?? ' ' }}</td>
          <td>{{ student.grades[subj.subject.id]?.second ?? ' ' }}</td>
          <td>{{ student.grades[subj.subject.id]?.ave ?? ' ' }}</td>
        </ng-container>
      </tr>
    </tbody>
  </table>
</div>

<div
  *ngIf="loading"
  class="d-flex justify-content-center align-items-center my-5"
  style="min-height: 150px"
>
  <div class="spinner-border text-primary" role="status"></div>
</div>
