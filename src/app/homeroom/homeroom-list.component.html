<h1>Homeroom Management</h1>

<table class="table table-hover table-sm text-center">
  <thead>
    <tr>
      <th style="width: 5%;">#</th>
      <th style="width: 10%;">Grade Level</th>
      <th style="width: 15%;">Section</th>
      <th style="width: 20%;">Strand</th> 
      <th style="width: 10%;">School Year</th>
      <th style="width: 20%;">Actions</th>
    </tr>
  </thead>

  <tbody>
    <tr *ngFor="let h of homerooms; let i = index">
      <td>{{ h.id }}</td>
      <td>{{ h.grade_level }}</td>
      <td>{{ h.section }}</td>
      <td>{{ h.strand}}</td>
      <td>{{ h.school_year }}</td>
      <td>
        <a [routerLink]="['./', h.id, 'FIRST SEMESTER']" routerLinkActive="active"  class="btn btn-sm btn-primary">Consolidated Sheet</a>
        <a [routerLink]="['./locking-history', h.id]" routerLinkActive="active"  class="btn btn-sm btn-success ml-3">Locking History</a>
      </td>
    </tr>
  </tbody>
</table>

<div *ngIf="loading" class="d-flex justify-content-center align-items-center my-5" style="min-height: 150px;">
  <div class="spinner-border text-primary" role="status"></div>
</div>

<ng-template #noData>
  <p>No homeroooms.</p>
</ng-template>