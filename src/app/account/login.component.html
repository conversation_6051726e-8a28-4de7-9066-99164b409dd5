<div
  style="
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  "
>
  <div
    style="
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 5px 5px 20px #000000;
      max-width: 600px;
      width: 100%;
    "
  >
    <h3 class="card-header">Enter your credentials</h3>
    <div class="card-body">
      <form [formGroup]="form" (ngSubmit)="onSubmit()">
        <div class="form-group">
          <label>Username</label>
          <input
            type="text"
            formControlName="username"
            class="form-control"
            [ngClass]="{ 'is-invalid': submitted && f.username.errors }"
          />
          <div *ngIf="submitted && f.username.errors" class="invalid-feedback">
            <div *ngIf="f.username.errors.required">Username is required</div>
            <div *ngIf="f.username.errors.username">Username is invalid</div>
          </div>
        </div>
        <div class="form-group">
          <label>Password</label>
          <input
            type="password"
            formControlName="password"
            class="form-control"
            [ngClass]="{ 'is-invalid': submitted && f.password.errors }"
          />
          <div *ngIf="submitted && f.password.errors" class="invalid-feedback">
            <div *ngIf="f.password.errors.required">Password is required</div>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group col">
            <button [disabled]="loading" class="btn btn-primary" style="width: 100%;">
              <span
                *ngIf="loading"
                class="spinner-border spinner-border-sm mr-1"
              ></span>
              Login
            </button>
            <!-- <a routerLink="/account/register" routerLinkActive="active" class="btn btn-link">Register</a> -->
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
