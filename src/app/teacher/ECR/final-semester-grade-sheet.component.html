
<div class="w-100 d-flex justify-content-end my-3">
  <button class="btn btn-sml btn-primary" (click)="printGradeSheet()">Print Grade</button>
</div>

<div id="print-header" class="mb-4" *ngIf="subject$ | async as subject">
  <strong class="text-center">Grading Period: {{ subject.semester }} FINAL GRADE</strong>
</div>

<table class="table table-bordered table-hover table-sm">
  <thead>
    <tr>
      <th style="width: 20%">Lastname</th>
      <th style="width: 20%;">Firstname</th>
      <th style="width: 5%" class="text-center">First Quarter</th>
      <th style="width: 5%" class="text-center">Second Quarter</th>
      <th style="width: 5%" class="text-center">Final Grade</th>
      <th style="width: 10%" class="text-center">Remarks</th>
      <th style="width: 20%" class="text-center">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngIf="students?.length === 0">
      <td colspan="8" class="text-center text-muted">No students enrolled yet boii.</td>
    </tr>
    <tr *ngFor="let student of students"> 
      <td>{{ student.lastName }}</td>
      <td>{{ student.firstName }}</td>
      <td class="text-center">{{ student.firstQuarter }}</td>
      <td class="text-center">{{  student.secondQuarter }}</td>
      <td class="text-center">{{ student.average }}</td>
      <td class="text-center font-weight-bold"  [ngClass]="{
      'text-success': student.remarks === 'PASSED',
      'text-danger': student.remarks === 'FAILED'
    }">{{ student.remarks }}</td>
      <td class="text-center">{{ student.description }}</td>
    </tr>
  </tbody>
</table>

<div *ngIf="loading" class="d-flex justify-content-center align-items-center my-5" style="min-height: 150px;">
  <div class="spinner-border text-primary" role="status"></div>
</div>

