<div class="border border-1 border-blackp p-2 mb-4">
  <h4 class="m-0">{{ quarter }}: {{ type }}</h4>
</div>

<div class="d-flex justify-content-between p-3">
  <div style="width: 75%">
    <table class="table table-bordered table-sm">
      <thead>
        <tr>
          <!-- <td style="width: 2%" class="text-center">#</td> -->
          <th style="width: 10%">Perfect Score</th>
          <th style="width: 30%">Description</th>
          <th style="width: 20%">Quiz Created</th>
          <th style="width: 10%" class="text-center">Edit Score</th>
          <th style="width: 15%" class="text-center">Actions</th>
          <th style="width: 10%">Delete Score</th>
        </tr>
      </thead>
      <tbody *ngIf="quizzes && quizzes.length > 0; else noQuizzes">
        <tr *ngFor="let quiz of quizzes">
          <!-- <td>{{ quiz.id }}</td> -->
          <td>
            <div
              *ngIf="editingQuizId !== quiz.id; else editHps"
              class="font-weight-bold text-center"
            >
              {{ quiz.hps }}
            </div>
            <ng-template #editHps>
              <form [formGroup]="quizForms[quiz.id]">
                <input
                  type="number"
                  class="form-control form-control-sm"
                  formControlName="hps"
                />
              </form>
            </ng-template>
          </td>
          <td>
            <div *ngIf="editingQuizId !== quiz.id; else editDesc">
              {{ quiz.description }}
            </div>
            <ng-template #editDesc>
              <form [formGroup]="quizForms[quiz.id]">
                <textarea
                  class="form-control form-control-sm"
                  formControlName="description"
                  rows="1"
                  placeholder="description is optional"
                ></textarea>
              </form>
            </ng-template>
          </td>
          <td>{{ quiz.createdAt }}</td>
          <td>
            <button
              *ngIf="editingQuizId !== quiz.id"
              class="btn btn-sm btn-info"
              (click)="onEdit(quiz)"
              [disabled]="locked"
            >
              Edit
            </button>
            <button
              *ngIf="editingQuizId === quiz.id"
              class="btn btn-sm btn-success"
              (click)="updateQuiz(quiz)"
              [disabled]="locked"
            >
              Save
            </button>
          </td>

          <td class="d-flex justify-content-between">
            <a
              [routerLink]="['./add', quiz.id]"
              [queryParams]="{ hps: quiz.hps }"
              class="btn btn-sm btn-success mr-3"
              [class.disabled]="locked"
              [attr.tabindex]="locked ? -1 : 0"
              >Add Scores</a
            >
            <a
              [routerLink]="['./edit', quiz.id]"
              [queryParams]="{ hps: quiz.hps }"
              class="btn btn-sm btn-primary"
              [class.disabled]="locked"
              [attr.tabindex]="locked ? -1 : 0"
              >Update Scores</a
            >
          </td>

          <td>
            <button
              [disabled]="quiz.isDeleting || locked"
              (click)="deleteQuiz(quiz.id)"
              class="btn btn-sm btn-danger btn-delete-account"
            >
              <span
                *ngIf="quiz.isDeleting"
                class="spinner-border spinner-border-sm"
              ></span>
              <span *ngIf="!quiz.isDeleting">Delete</span>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div style="width: 20%" class="pb-3 px-3">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <fieldset [disabled]="locked">
        <h4 class="mb-3">Add quiz</h4>
        <div class="mb-2 d-flex">
          <label class="form-label d-inline w-50">Perfect Score:</label>
          <input
            type="number"
            class="form-control form-control-sm w-50"
            [ngClass]="{ 'is-invalid': submitted && f.hps.errors }"
            formControlName="hps"
          />
        </div>
        <div *ngIf="submitted && f.hps.errors" class="invalid-feedback d-block">
          <div *ngIf="f.hps.errors.required">Perfect Score is required</div>
        </div>
        <div class="mb-2">
          <label class="form-label d-inline">Description:</label>
          <textarea
            class="form-control form-control-sm"
            [ngClass]="{
              'is-invalid': submitted && f.description.errors?.maxlength
            }"
            formControlName="description"
            rows="2"
            placeholder="description is optional "
          ></textarea>
          <div
            *ngIf="submitted && f.description.errors?.maxlength"
            class="invalid-feedback d-block"
          >
            <div>Description must not exceed 255 characters</div>
          </div>
        </div>

        <button [disabled]="loading || locked" class="btn btn-primary">
          <span
            *ngIf="loading"
            class="spinner-border spinner-border-sm mr-1"
          ></span>
          Add quiz
        </button>

        <ng-template #noQuizzes>
          <tbody>
            <tr>
              <td colspan="10" class="h1 text-center">No quizzes found.</td>
            </tr>
          </tbody>
        </ng-template>
      </fieldset>
    </form>
  </div>
</div>
