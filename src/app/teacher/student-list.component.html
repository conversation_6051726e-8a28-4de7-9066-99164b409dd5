<form (ngSubmit)="submitEnrollmentDecisions()" class="py-4">
  <h3>Students partially enrolled in this subject</h3>
  <table class="table table-hover table-sm">
    <thead>
      <tr>
        <th style="width: 5%">
          <input
            type="checkbox"
            [(ngModel)]="selectAll"
            (change)="toggleSelectAll()"
            name="selectAll"
          />
        </th>
        <!-- <th style="width: 10%;">Enrollment Id</th> -->
        <th style="width: 20%">Lastname</th>
        <th style="width: 20%">Firstname</th>
        <th style="width: 5%">Sex</th>
        <th style="width: 20%">Address</th>
        <!-- <th style="width: 20%">Name of Guardian</th>
        <th style="width: 20%">Contact Number</th> -->
      </tr>
    </thead>
    <tbody>
      <tr *ngIf="!isLoading && students.length === 0">
        <td colspan="7" class="text-center">
          <h5 class="m-0">No students has been found.</h5>
        </td>
      </tr>
      <tr *ngFor="let student of students; let i = index">
        <td>
          <input
            type="checkbox"
            [(ngModel)]="student.selected"
            name="selected{{ i }}"
          />
        </td>
        <!-- <td>{{ student.enrollment_id }}</td> -->
        <td>{{ student.lastName }}</td>
        <td>{{ student.firstName }}</td>
        <td>{{ student.sex }}</td>
        <td>{{ student.address }}</td>
        <!-- <td>{{ student.guardian_name }}</td>
        <td>{{ student.guardian_contact }}</td> -->
      </tr>
    </tbody>
  </table>

  <div class="d-flex align-items-center">
    <button type="submit" class="mr-3 btn btn-sm btn-primary">
      Submit Selected
    </button>

    <a class="btn btn-success btn-sm" [routerLink]="['/teacher', id]"
      >Go to ECR</a
    >
  </div>
</form>
