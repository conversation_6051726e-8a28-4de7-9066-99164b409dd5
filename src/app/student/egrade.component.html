<div class="py-2 px-4">
  <h1>E-Grade</h1>

  <div class="form-group col-md-4">
    <label for="schoolTerm">School Term</label>
    <select id="schoolTerm" class="form-control" [(ngModel)]="selectedSemester">
      <option value="">-- Select School Term --</option>
      <option value="FIRST SEMESTER">1st Semester S.Y. {{ schoolYear }}</option>
      <option value="SECOND SEMESTER">2nd Semester S.Y. {{ schoolYear }}</option>
    </select>
  </div>

  <table class="table table-hover table-sm">
    <thead>
      <tr>
        <th style="width: 10%">Code</th>
        <th style="width: 50%">Subject Name</th>
        <th style="width: 20%">Teacher</th>
        <th style="width: 10%">1ST</th>
        <th style="width: 10%">2ND</th>
        <th style="width: 10%">SFG</th> 
      </tr>   
    </thead>      
    <tbody>     
      <!-- If no semester selected -->  
      <tr *ngIf="!selectedSemester">
        <td colspan="6" class="text-center py-6">
          <h1 class="text-xl font-bold">Select semester</h1>  
        </td>     
      </tr>

      <!-- If SECOND SEMESTER selected but no 2nd sem subjects -->
      <tr
        *ngIf="selectedSemester === 'SECOND SEMESTER' && !hasSecondSemSubjects"
      >
        <td colspan="6" class="text-center py-6">
          <h1>You're not yet enrolled in 2nd sem</h1>
        </td>
      </tr>

      <!-- Otherwise, render filtered subjects -->
      <tr *ngFor="let a of filteredSubjects; let i = index">
        <td>{{ a.subjectCode }}</td>
        <td>{{ a.subjectName }}</td>
        <td>
          {{ a.teacher ? a.teacher.lastName + " " + a.teacher.firstName : "" }}
        </td>
        <!-- first quarter grade -->
        <td>{{ a.firstQuarter || "" }}</td>
        <!-- second quarter grade -->
        <td>{{ a.secondQuarter || "" }}</td>
        <!-- average of two quarter grades -->
        <td>{{ a.finalAverage || "" }}</td>
      </tr>
    </tbody>
  </table>
</div>
