[33mcommit 2edce8f2164f39b5221fa475da1958a349fe2d56[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m)[m
Author: <PERSON> <<EMAIL>>
Date:   Sun Aug 31 17:10:22 2025 +0800

    env

[33mcommit c1566b2b561acc8d910561487374be0e5a5fb128[m
Author: Kurt <<EMAIL>>
Date:   Sun Aug 31 16:48:11 2025 +0800

    update

[33mcommit 0b56f66595a6af1d7882a9e49f2223140e5a39e4[m
Author: Kurt <<EMAIL>>
Date:   Thu Aug 28 19:14:56 2025 +0800

    changes

[33mcommit a111ce1e46ab56003b1d13cb5514f9802c05b455[m
Author: Kort Buset <<EMAIL>>
Date:   Wed Aug 27 22:39:53 2025 +0800

    'update'

[33mcommit b30682bcf6d9ce467846d37c2d55d1e0345086a1[m
Author: <PERSON> <<EMAIL>>
Date:   Wed Aug 27 11:20:32 2025 +0800

    'update'

[33mcommit 4852703734e78a1eab70f8516f97d647c81ed13c[m
Author: Kurt Buset <<EMAIL>>
Date:   Sun Aug 24 20:32:04 2025 +0800

    'update'

[33mcommit d494a22f1d0bc0b6dfd912db4c1260c06f1c5f73[m
Author: Kurt <<EMAIL>>
Date:   Fri Aug 22 22:08:17 2025 +0800

    update

[33mcommit 8373b6342533f0e846a0153dcd7ae68cf38f1dcb[m
Author: Kurt <<EMAIL>>
Date:   Wed Aug 20 20:30:44 2025 +0800

    major update

[33mcommit 24b81d2f54830d67c077f2019ab6fcb18f88ea41[m
Author: Kurt <<EMAIL>>
Date:   Thu Aug 14 14:02:51 2025 +0800

    update

[33mcommit 5117ffaf48e4a96461f752679ba7814d12f60a66[m
Author: Kurt <<EMAIL>>
Date:   Thu Aug 14 13:58:50 2025 +0800

    update

[33mcommit cadb913c9d6cbc243089438c08f263f01e37e717[m
Author: Kurt <<EMAIL>>
Date:   Sat Aug 9 17:50:15 2025 +0800

    update

[33mcommit 5e2e684ddde8ce4a471f44e4ed643018cdb3abac[m
Author: Kurt <<EMAIL>>
Date:   Fri Aug 8 22:36:30 2025 +0800

    update

[33mcommit 421a1983bf7b5fe19793d256a63a29db3b641375[m
Author: Kurt <<EMAIL>>
Date:   Fri Aug 8 22:16:11 2025 +0800

    adding quiz date and loading animation

[33mcommit 665bf6ecaee218be7043f680e24c1f5dc2242e96[m
Author: Kurt <<EMAIL>>
Date:   Fri Aug 8 11:35:06 2025 +0800

    changes

[33mcommit d2fac134d886d1dd49bad1dadaae21ece5f60ecf[m
Merge: 76a1e93 8693c98
Author: Kurt <<EMAIL>>
Date:   Fri Aug 8 11:28:43 2025 +0800

    Merge branch 'main' of https://github.com/kurtbuset/frontend-grading-system-standalone-based

[33mcommit 76a1e93c2b450d7ad53856c8156b5050350905e7[m
Author: Kurt <<EMAIL>>
Date:   Fri Aug 8 11:28:21 2025 +0800

    changing backend service

[33mcommit 8693c9835212ed04dc609687abb094bf044a5daf[m
Merge: 87c36a5 10ffa5e
Author: Erl Christian R. Trangia <<EMAIL>>
Date:   Fri Aug 8 10:21:06 2025 +0800

    Merge pull request #9 from kurtbuset/Trangia_PM
    
    Updating Documentation

[33mcommit 10ffa5e10240687b1fac7ea7a973439ba2be9419[m
Author: Erl Christian Trangia <<EMAIL>>
Date:   Fri Aug 8 10:20:27 2025 +0800

    Updating Documentation

[33mcommit 87c36a53e75895795d597c4ef6ccdeda8d9b4cb1[m
Author: Kurt <<EMAIL>>
Date:   Thu Aug 7 19:23:13 2025 +0800

    update

[33mcommit 7de3f54ec7554fbb5687142222a1c2251936b88f[m
Author: Kurt <<EMAIL>>
Date:   Mon Aug 4 17:10:52 2025 +0800

    change env

[33mcommit 2fa7389d7d2068e3064cf95e4dd50b69d0fde746[m
Author: Kurt <<EMAIL>>
Date:   Mon Aug 4 17:09:16 2025 +0800

    adding loading effects

[33mcommit 390b70e1eea51fb1aaa1a9bb282367f6ebc6e217[m
Author: Kurt <<EMAIL>>
Date:   Mon Aug 4 14:18:06 2025 +0800

    update

[33mcommit 44ef4795d025dfbfa05acb5adfb2eff5350eb723[m
Author: Kurt <<EMAIL>>
Date:   Sat Aug 2 22:20:10 2025 +0800

    test

[33mcommit a1655f6dd629b0e979e9de7061ac79892706920b[m
Author: Kurt <<EMAIL>>
Date:   Sat Aug 2 20:46:00 2025 +0800

    adding vercel

[33mcommit 62c18419bebfd46fe668cefde2cf7b11382c327c[m
Author: Kurt <<EMAIL>>
Date:   Sat Aug 2 20:16:09 2025 +0800

    change env apiUrl to use prod

[33mcommit edfeac56199749fdcb3006f1010948fb7edd2e19[m
Merge: 555307a c869757
Author: Erl Christian R. Trangia <<EMAIL>>
Date:   Fri Aug 1 21:15:28 2025 +0800

    Merge pull request #7 from kurtbuset/Trangia_PM
    
    editing README.md

[33mcommit c8697573aad2e8d44e47b6a768e08b7b2d842c13[m
Author: Erl Christian Trangia <<EMAIL>>
Date:   Fri Aug 1 21:13:11 2025 +0800

    editing README.md

[33mcommit 555307abaa0424185c79d1f2ff4ba1f03176ad36[m
Author: Kurt <<EMAIL>>
Date:   Mon Jul 28 21:27:26 2025 +0800

    update

[33mcommit 3d50a0c41c76274902bbc63f5e8d7a9dc9e85347[m
Author: Kurt <<EMAIL>>
Date:   Tue Jul 22 10:27:06 2025 +0800

    admin partial

[33mcommit 82afecc616a61e5e5fae397a4ab6bf95a25b60dc[m
Author: Kurt <<EMAIL>>
Date:   Sat Jul 19 14:29:03 2025 +0800

    adding printable pdf

[33mcommit 27b43cb221bcc974bac7f007099ee0c4d458d169[m
Author: Kurt <<EMAIL>>
Date:   Thu Jul 17 22:16:14 2025 +0800

    update

[33mcommit 71e4ea9b84458d3eef76d5a3cbe3162111588f9c[m
Author: Kurt <<EMAIL>>
Date:   Thu Jul 17 18:56:27 2025 +0800

    update

[33mcommit 32940ea5e164fc3865b487eb111b2c4d9e26f178[m
Author: Kurt <<EMAIL>>
Date:   Wed Jul 16 22:20:34 2025 +0800

    update

[33mcommit b882d6e2490b94cb7c8bac69f3efb1ec0cc32bb0[m
Author: Kurt <<EMAIL>>
Date:   Tue Jul 15 00:48:52 2025 +0800

    update

[33mcommit c2450da1c4261aaa5ee5e29779c2371000e69385[m
Author: Kurt <<EMAIL>>
Date:   Sun Jul 13 20:36:06 2025 +0800

    update

[33mcommit e9e40dfd6906423354735eccf0bd3623fd25eaff[m
Author: Kurt <<EMAIL>>
Date:   Fri Jul 11 13:22:04 2025 +0800

    update

[33mcommit a487e7bb885f63b72975670c61cd33afaf1425cc[m
Author: Kurt <<EMAIL>>
Date:   Wed Jul 9 22:18:25 2025 +0800

    update

[33mcommit 20acdf15d5bdc4f8f62009759ddf945fd6d73b84[m
Author: Kurt <<EMAIL>>
Date:   Wed Jul 9 12:52:20 2025 +0800

    update

[33mcommit 9e7ba0601e3cd1a3676122b952753068a2e1060d[m
Author: Kurt <<EMAIL>>
Date:   Mon Jul 7 18:12:30 2025 +0800

    update

[33mcommit 2761d47cc4fda4e20ac756447bd06040bfda6c8d[m
Author: Kurt <<EMAIL>>
Date:   Thu Jul 3 12:15:25 2025 +0800

    update

[33mcommit 1099037998338f36e579288ada1aa620046f2bca[m
Author: Kurt <<EMAIL>>
Date:   Mon Jun 30 21:59:51 2025 +0800

    student partial wala pay accept/denied feature

[33mcommit 2538f1f7715223f0df36222bb5205e3ef6d7d652[m
Author: Kurt <<EMAIL>>
Date:   Mon Jun 30 18:17:08 2025 +0800

    partial student

[33mcommit a2c67516ff700d1e60297f3475c66615e82a442b[m
Author: Kurt <<EMAIL>>
Date:   Sat Jun 28 19:43:58 2025 +0800

    partial subject part

[33mcommit 94cdaeca829fd55249126e4f6bc9ed56515a0a9b[m
Author: Kurt <<EMAIL>>
Date:   Fri Apr 25 15:08:12 2025 +0800

    initial commit
