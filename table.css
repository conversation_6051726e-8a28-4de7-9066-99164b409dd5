* {
  margin: 0px;
  padding: 0;
  font-family: Arial, Helvetica, sans-serif;
}


.outer-wrapper {
  margin: 10px;
  margin-left: 20px;
  margin-right: 20px;
  border: 1px solid black;
  border-radius: 4px;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.9);
  max-width: fit-content;
  max-height: fit-content;
}
.table-wrapper {
  overflow-y: scroll;
  overflow-x: scroll;
  height: fit-content;
  max-height: 66.4vh;

  margin-top: 22px;

  margin: 15px;
  padding-bottom: 20px;
}

table {
  min-width: max-content;

  border-collapse: separate;
  border-spacing: 0px;
}

table th {
  position: sticky;
  top: 0px;

  background-color: #133b5c;
  color: rgb(241, 245, 179);

  text-align: center;
  font-weight: normal;
  font-size: 18px;
  outline: 0.7px solid black;
  border: 1.5px solid black;
}

table th,
table td {
  padding: 15px;
  padding-top: 10px;
  padding-bottom: 10px;
}

table td {
  text-align: left;

  font-size: 15px;
  border: 1px solid rgb(177, 177, 177);
  padding-left: 20px;
}
