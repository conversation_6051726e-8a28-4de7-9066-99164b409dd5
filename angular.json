{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"frontend-angular-17": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/frontend-angular-17", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "less", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.less", "node_modules/ag-grid-community/styles/ag-grid.css", "node_modules/ag-grid-community/styles/ag-theme-balham.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "frontend-angular-17:build:production"}, "development": {"buildTarget": "frontend-angular-17:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "frontend-angular-17:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "less", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.less"], "scripts": []}}}}}, "cli": {"analytics": false}}